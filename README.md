# Simple Proxy

A high-performance reverse proxy built with [Pingora](https://github.com/cloudflare/pingora) that provides HTTP proxy functionality for backend services.

## Features

- **Reverse Proxy**: Routes HTTP requests to backend services
- **Header Modification**: Automatically adds custom headers to requests and responses
- **High Performance**: Built on Pingora, Cloudflare's high-performance HTTP proxy framework
- **Logging & Tracing**: Comprehensive request/response logging with tracing
- **Health Checks**: Built-in health check endpoint support

## Architecture

The proxy consists of two main components:

1. **SimpleProxy** (`src/lib.rs`): The core proxy implementation that handles request routing and header modification
2. **Backend Server** (`examples/server.rs`): A sample Axum HTTP server that the proxy forwards requests to

## Quick Start

### Prerequisites

- Rust 1.70+ (2021 edition)
- Cargo

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd simple-proxy

# Build the project
cargo build --release

# Run the proxy server
cargo run --release
```

### Running the Example

1. **Start the backend server** (in one terminal):

```bash
# Run the example Axum server
RUST_LOG=trace cargo run --example server
```

The backend server will start on `http://127.0.0.1:3000`

2. **Start the proxy server** (in another terminal):

```bash
# Run the proxy
RUST_LOG=trace cargo run --release
```

The proxy will start on `http://0.0.0.0:8080`

3. **Test the proxy**:

```bash
# Test health check through proxy
curl http://localhost:8080/health

# Test user endpoints through proxy
curl http://localhost:8080/users
curl -X POST http://localhost:8080/users \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'
```

## Configuration

### Proxy Configuration

The proxy is configured in `src/main.rs`:

- **Proxy Address**: `0.0.0.0:8080` (configurable)
- **Backend Address**: `127.0.0.1:3000` (configurable in `SimpleProxy::upstream_peer`)
- **Custom Headers**: Automatically adds `user-agent: SimpleProxy/0.1` to requests
- **Response Headers**: Adds `x-simple-proxy: v0.1` and preserves/modifies `server` header

### Backend Server Configuration

The example backend server (`examples/server.rs`) provides:

- **User Management API**: CRUD operations for users
- **Health Check**: `/health` endpoint
- **Password Hashing**: Argon2 password hashing
- **In-Memory Storage**: DashMap for user storage
- **Comprehensive Logging**: Request/response tracing

## API Endpoints

When accessed through the proxy (`http://localhost:8080`):

### Health Check

```http
GET /health
```

Returns server health status.

### User Management

```http
GET /users                    # List all users
GET /users/{id}              # Get user by ID
POST /users                  # Create new user
PUT /users/{id}              # Update user
DELETE /users/{id}           # Delete user
```

### Example API Usage

```bash
# Create a user
curl -X POST http://localhost:8080/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword",
    "name": "John Doe"
  }'

# Get all users
curl http://localhost:8080/users

# Get specific user
curl http://localhost:8080/users/1

# Update user
curl -X PUT http://localhost:8080/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "John Updated"
  }'

# Delete user
curl -X DELETE http://localhost:8080/users/1
```

## Development

The project includes a comprehensive test suite for backend service. Run the tests with:

```bash
make test
```

### Project Structure

```
simple-proxy/
├── src/
│   ├── lib.rs          # Core proxy implementation
│   └── main.rs         # Proxy server entry point
├── examples/
│   └── server.rs       # Example backend server
├── tests/              # Unit tests
└── README.md
```

### Running Tests

```bash
# Run all tests
cargo test

# Run tests with output
cargo test -- --nocapture

# Run specific test
cargo test test_create_user
```

### Building

```bash
# Debug build
cargo build

# Release build
cargo build --release

# Build specific example
cargo build --example server
```

## Dependencies

### Core Dependencies

- **pingora**: High-performance HTTP proxy framework
- **tracing**: Application-level tracing
- **tracing-subscriber**: Tracing subscriber implementation

### Example Server Dependencies

- **axum**: Web framework
- **tokio**: Async runtime
- **serde**: Serialization/deserialization
- **chrono**: Date and time handling
- **dashmap**: Concurrent HashMap
- **argon2**: Password hashing
- **tower-http**: HTTP middleware

## Customization

### Adding Custom Headers

Modify the `upstream_request_filter` method in `src/lib.rs`:

```rust
async fn upstream_request_filter(
    &self,
    _session: &mut Session,
    upstream_request: &mut RequestHeader,
    _ctx: &mut Self::CTX,
) -> Result<()> {
    // Add custom headers
    upstream_request.insert_header("x-custom-header", "custom-value")?;
    upstream_request.insert_header("user-agent", "SimpleProxy/0.1")?;
    Ok(())
}
```

### Changing Backend Configuration

Modify the `upstream_peer` method in `src/lib.rs`:

```rust
async fn upstream_peer(
    &self,
    _session: &mut Session,
    _ctx: &mut Self::CTX
) -> Result<Box<HttpPeer>> {
    // Change backend address here
    let peer = HttpPeer::new("your-backend:port".to_string(), false, "your-hostname".to_string());
    Ok(Box::new(peer))
}
```

## Monitoring and Logging

The proxy provides comprehensive logging:

- **Request Headers**: Logged on incoming requests
- **Response Headers**: Logged on outgoing responses
- **Upstream Peer**: Logged when connecting to backend
- **Error Handling**: Warnings for header modification failures

### Log Levels

```bash
# Set log level
RUST_LOG=debug cargo run --release
RUST_LOG=info cargo run --release
RUST_LOG=warn cargo run --release
```

## Performance

- **High Throughput**: Built on Pingora's high-performance architecture
- **Low Latency**: Minimal overhead for request processing
- **Concurrent Handling**: Efficient handling of multiple concurrent requests
- **Memory Efficient**: Optimized memory usage for header processing

## Security Considerations

- **Input Validation**: Backend server validates all inputs
- **Password Hashing**: Argon2 password hashing for security
- **Header Sanitization**: Careful handling of request/response headers
- **Error Handling**: Graceful error handling without information leakage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is distributed under the terms of MIT.

See [LICENSE](./LICENSE.md) for details.

Copyright 2025 Musakenshi
