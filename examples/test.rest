# Rest Client


@host = http://localhost:8080

### Create a user
POST {{host}}/users
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "name": "Test User"
}

### List users
GET {{host}}/users
Content-Type: application/json

### Get user by id
GET {{host}}/users/1

### Update user
PUT {{host}}/users/1
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "name": "Test User"
}

### Delete user
DELETE {{host}}/users/1

### Health check
GET {{host}}/health