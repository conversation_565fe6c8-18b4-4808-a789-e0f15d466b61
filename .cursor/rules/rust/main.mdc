---
description:
globs: Cargo.toml,*.rs
alwaysApply: false
---
# 🦀 RUST PROJECT RULE SET SYSTEM

> **TL;DR:** This system provides comprehensive guidelines for Rust project development with complexity-based rule loading, following industry best practices for scalable, maintainable Rust code.

## 🔍 PROJECT COMPLEXITY DETERMINATION

```mermaid
graph TD
    Start["New Rust Project"] --> Analyze["Analyze Project Requirements"]
    Analyze --> Q1{"Multiple distinct<br>functional domains?"}
    Q1 -->|Yes| Q2{"Expected LOC<br>> 10,000?"}
    Q1 -->|No| Q3{"Single domain with<br>multiple components?"}

    Q2 -->|Yes| Complex["COMPLEX PROJECT<br>Multi-crate workspace"]
    Q2 -->|No| Q4{"Shared libraries<br>needed?"}
    Q4 -->|Yes| Complex
    Q4 -->|No| Simple["SIMPLE PROJECT<br>Single crate"]

    Q3 -->|Yes| Q5{"Need separate<br>deployable units?"}
    Q3 -->|No| Simple
    Q5 -->|Yes| Complex
    Q5 -->|No| Simple

    Complex --> LoadComplex["Load Complex Rules"]
    Simple --> LoadSimple["Load Simple Rules"]

    LoadComplex --> Features["Load Feature-Specific Rules"]
    LoadSimple --> Features

    Features --> Web{"Web Framework<br>needed?"}
    Features --> DB{"Database<br>access needed?"}
    Features --> CLI{"CLI interface<br>needed?"}
    Features --> GRPC{"gRPC/Protobuf<br>needed?"}
    Features --> Concurrent{"Heavy<br>concurrency?"}
    Features --> Config{"Complex config<br>or templating?"}
    Features --> Observability{"Metrics & tracing<br>needed?"}
    Features --> ConfigMgmt{"Dynamic config<br>management?"}

    Web -->|Yes| WebRules["Load Axum Rules"]
    DB -->|Yes| DBRules["Load Database Rules"]
    CLI -->|Yes| CLIRules["Load CLI Rules"]
    GRPC -->|Yes| GRPCRules["Load Protobuf & gRPC Rules"]
    Concurrent -->|Yes| ConcurrencyRules["Load Concurrency Rules"]
    Config -->|Yes| ToolsRules["Load Tools & Config Rules"]
    Observability -->|Yes| ObservabilityRules["Load Observability Rules"]
    ConfigMgmt -->|Yes| ConfigMgmtRules["Load Configuration Rules"]

    style Start fill:#4da6ff,stroke:#0066cc,color:white
    style Complex fill:#d94dbb,stroke:#a3378a,color:white
    style Simple fill:#4dbb5f,stroke:#36873f,color:white
    style LoadComplex fill:#ffa64d,stroke:#cc7a30,color:white
    style LoadSimple fill:#4dbbbb,stroke:#368787,color:white
```

## 📊 COMPLEXITY INDICATORS

### Simple Project Indicators
- **Single Domain**: One clear functional area (CLI tool, library, simple service)
- **Codebase Size**: Expected < 10,000 lines of code
- **Team Size**: 1-3 developers
- **Dependencies**: Minimal external integrations
- **Deployment**: Single binary or library

### Complex Project Indicators
- **Multiple Domains**: Authentication, business logic, data processing, etc.
- **Codebase Size**: Expected > 10,000 lines of code
- **Team Size**: 4+ developers or multiple teams
- **Shared Components**: Common libraries across multiple binaries
- **Microservices**: Multiple deployable units

## 🔄 RULE LOADING HIERARCHY

```mermaid
graph TD
    Main["main.mdc"] --> Core["Core Rules<br>(Always Loaded)"]
    Main --> Complexity{"Project<br>Complexity?"}

    Core --> Quality["Code Quality Rules"]
    Core --> Dependencies["Dependency Management"]
    Core --> Types["Type System Patterns"]
    Core --> Performance["Performance Guidelines"]
    Core --> Security["Security Standards"]
    Core --> API["API Design Principles"]
    Core --> Patterns["Design Patterns"]
    Core --> ErrorHandling["Error Handling"]

    Complexity -->|Simple| SimpleRules["Simple Project Rules"]
    Complexity -->|Complex| ComplexRules["Complex Project Rules"]

    SimpleRules --> SingleCrate["Single Crate Structure"]
    ComplexRules --> Workspace["Workspace Management"]
    ComplexRules --> MultiCrate["Multi-crate Architecture"]

    SimpleRules & ComplexRules --> FeatureDetection["Feature Detection"]

    FeatureDetection --> WebFeature{"Web Framework?"}
    FeatureDetection --> DBFeature{"Database?"}
    FeatureDetection --> CLIFeature{"CLI Interface?"}
    FeatureDetection --> GRPCFeature{"gRPC/Protobuf?"}
    FeatureDetection --> SerdeFeature{"Serialization?"}
    FeatureDetection --> BuilderFeature{"Complex Types?"}
    FeatureDetection --> ObservabilityFeature{"Metrics & Tracing?"}
    FeatureDetection --> ConfigFeature{"Dynamic Config?"}

    WebFeature -->|Yes| AxumRules["Axum Framework Rules"]
    DBFeature -->|Yes| SQLxRules["SQLx Database Rules"]
    CLIFeature -->|Yes| CLIRules["CLI Application Rules"]
    GRPCFeature -->|Yes| GRPCRules["Protobuf & gRPC Rules"]
    SerdeFeature -->|Yes| SerdeRules["Serde Best Practices"]
    BuilderFeature -->|Yes| TypedBuilderRules["TypedBuilder Rules"]
    ObservabilityFeature -->|Yes| ObservabilityRules["Observability Rules"]
    ConfigFeature -->|Yes| ConfigurationRules["Configuration Rules"]

    style Main fill:#4da6ff,stroke:#0066cc,color:white
    style Core fill:#ffa64d,stroke:#cc7a30,color:white
    style SimpleRules fill:#4dbb5f,stroke:#36873f,color:white
    style ComplexRules fill:#d94dbb,stroke:#a3378a,color:white
```

## 📋 CORE PRINCIPLES (ALWAYS APPLIED)

1. **Code Quality**: Follow DRY/SRP principles, function size limits
2. **Dependencies**: Workspace-first, security-focused dependency management
3. **Type System**: Leverage newtype patterns, phantom types, and zero-cost abstractions
4. **Performance**: Profile-driven optimization, memory-efficient patterns
5. **Security**: Input validation, secure secrets management, authorization
6. **API Design**: Ergonomic interfaces, builder patterns, comprehensive documentation
7. **File Organization**: Functionality-based structure, not type-based
8. **Error Handling**: Consistent error handling patterns with structured errors
9. **Testing**: Comprehensive unit test coverage with mocking strategies
10. **Documentation**: Clear, maintainable code documentation with examples

## 🚀 PROJECT INITIALIZATION WORKFLOW

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant System as Rule System
    participant Cargo as Cargo
    participant Files as File Structure

    Dev->>System: Initialize Rust project
    System->>System: Determine complexity
    System->>Dev: Complexity assessment result

    alt Simple Project
        System->>Cargo: cargo new project_name
        System->>Files: Create single crate structure
    else Complex Project
        System->>Cargo: cargo new --name workspace project_name
        System->>Files: Create workspace structure
        System->>Files: Initialize member crates
    end

    System->>System: Detect required features
    System->>Files: Apply feature-specific templates
    System->>Dev: Project ready for development
```

## 📝 FEATURE DETECTION CHECKLIST

```markdown
## Feature Detection for Rule Loading

### Web Framework Requirements
- [ ] HTTP server needed
- [ ] REST API endpoints
- [ ] OpenAPI documentation required
- [ ] Axum framework planned
- [ ] SSE (Server-Sent Events) needed
- [ ] → Load Axum rules if YES to any

### Database Requirements
- [ ] Database queries needed
- [ ] SQL database integration (PostgreSQL/SQLite)
- [ ] Database migrations required
- [ ] Repository pattern needed
- [ ] Connection pooling required
- [ ] → Load Database rules if YES to any

### Concurrency Requirements
- [ ] Multi-threading needed
- [ ] Async/await extensively used
- [ ] Shared state across threads
- [ ] Event-driven architecture
- [ ] Background task processing
- [ ] → Load Concurrency rules if YES to any

### Configuration Management Requirements
- [ ] Dynamic configuration reloading needed
- [ ] Multi-format config support (YAML/TOML/JSON)
- [ ] Configuration validation required
- [ ] Environment-based configuration overrides
- [ ] File system watching for config changes
- [ ] → Load Configuration rules if YES to any

### Observability Requirements
- [ ] Metrics collection needed
- [ ] Performance monitoring required
- [ ] Distributed tracing needed
- [ ] Health checks for dependencies
- [ ] Prometheus integration required
- [ ] → Load Observability rules if YES to any

### Tools & Configuration Requirements
- [ ] Complex configuration files
- [ ] Template rendering needed
- [ ] Structured logging required
- [ ] Data transformation logic
- [ ] JSON path extraction
- [ ] → Load Tools & Config rules if YES to any

### Serialization Requirements
- [ ] JSON handling required
- [ ] External API integration
- [ ] Configuration files
- [ ] camelCase JSON serialization
- [ ] → Load Serde rules if YES to any

### Builder Pattern Requirements
- [ ] Complex data structures (4+ fields)
- [ ] Optional fields in constructors
- [ ] Fluent API needed
- [ ] → Load TypedBuilder rules if YES to any

### Utility Libraries Requirements
- [ ] Authentication/JWT needed
- [ ] CLI interface required
- [ ] Random data generation
- [ ] Enhanced derive macros
- [ ] Custom error types
- [ ] → Load Utilities rules if YES to any

### CLI Application Requirements
- [ ] Command-line interface needed
- [ ] Multiple subcommands required
- [ ] Interactive prompts needed
- [ ] Progress bars or status indicators
- [ ] Configuration file support
- [ ] → Load CLI rules if YES to any

### Protobuf & gRPC Requirements
- [ ] Protocol buffers for data serialization
- [ ] gRPC service implementation needed
- [ ] Inter-service communication required
- [ ] Schema evolution support needed
- [ ] High-performance RPC required
- [ ] → Load Protobuf & gRPC rules if YES to any

### HTTP Client Requirements
- [ ] External API integration
- [ ] HTTP requests needed
- [ ] REST client functionality
- [ ] Authentication headers
- [ ] Retry/resilience patterns
- [ ] → Load HTTP Client rules if YES to any
```

## 🔧 RULE LOADING COMMANDS

Based on project analysis, load specific rule sets:

```bash
# For simple projects
# Loads: core + simple + detected features

# For complex projects
# Loads: core + complex + workspace + detected features

# Feature-specific loading examples:
# Web: core + axum + serde + utilities (JWT)
# Database: core + sqlx + utilities (error handling)
# CLI: core + cli + utilities (enum_dispatch + error handling)
# gRPC: core + protobuf-grpc + utilities (typed_builder + sanitization)
# Config Management: core + configuration + observability (metrics)
# Service Applications: core + configuration + observability + utilities
# Auth: core + utilities (JWT + validation)
```

## 📚 AVAILABLE RULE MODULES

| Module | File | Description |
|--------|------|-------------|
| **Core** | `core/code-quality.mdc` | Rust 2024, no unsafe, production-ready code |
| **Core** | `core/dependencies.mdc` | Centralized dependency management and workspace patterns |
| **Core** | `core/type-system.mdc` | Type system mastery, newtype patterns, phantom types |
| **Core** | `core/performance.mdc` | Performance optimization, SIMD, memory management |
| **Core** | `core/security.mdc` | Security patterns, Argon2 hashing, encryption |
| **Core** | `core/api-design.mdc` | Ergonomic API design, builder patterns, trait design |
| **Core** | `core/design-patterns.mdc` | Essential design patterns, actor model, strategy |
| **Simple** | `simple/single-crate.mdc` | Single crate project structure |
| **Complex** | `complex/workspace.mdc` | Multi-crate workspace management |
| **Web** | `features/axum.mdc` | Axum 0.8 patterns, OpenAPI with utoipa |
| **Database** | `features/database.mdc` | SQLx patterns, repository design, testing |
| **CLI** | `features/cli.mdc` | Clap 4.0+ patterns, subcommands, enum_dispatch |
| **Protobuf & gRPC** | `features/protobuf-grpc.mdc` | Prost/Tonic 0.13+, Inner types, MessageSanitizer, reflection |
| **Concurrency** | `features/concurrency.mdc` | Tokio, DashMap, async patterns |
| **Configuration** | `features/configuration.mdc` | Multi-format config, hot-reloading, validation patterns |
| **Observability** | `features/observability.mdc` | Metrics collection, distributed tracing, health checks |
| **Tools & Config** | `features/tools-and-config.mdc` | Tracing, YAML config, MiniJinja templates |
| **Utilities** | `features/utilities.mdc` | JWT auth, CLI tools, builders, enhanced derives |
| **HTTP Client** | `features/http-client.mdc` | reqwest patterns, error handling, retry logic |
| **Testing** | `quality/testing.mdc` | Unit testing standards |
| **Errors** | `quality/error-handling.mdc` | thiserror/anyhow patterns |

## 🎯 PROJECT TYPE EXAMPLES

### Simple Project Examples
- **CLI utilities** (grep clone, file converter, system tools)
- **Single-purpose libraries** (parsing, algorithms)
- **Simple HTTP servers** (< 10 endpoints)
- **Desktop applications** (single-window apps)

### Complex Project Examples
- **Workflow engines** (multi-node processing systems)
- **Multi-service applications** (auth + business + gateway)
- **Microservices with gRPC** (inter-service communication)
- **Enterprise applications** (multiple domains)
- **Database systems** with multiple engines
- **Distributed systems** with event processing
- **Production services** (requiring metrics, health checks, config reloading)
- **High-availability applications** (with observability and dynamic configuration)

## 🚀 MODERN RUST STACK PREFERENCES

Based on real-world experience, these are the recommended tools:

### Core Dependencies
- **Rust Edition**: 2024 (always latest)
- **Error Handling**: `thiserror` for libraries, `anyhow` for binaries
- **Async Runtime**: `tokio` with full features
- **Serialization**: `serde` with `camelCase` for JSON APIs

### Web Development Stack
- **Web Framework**: `axum` 0.8+
- **API Documentation**: `utoipa` + `utoipa-swagger-ui`
- **HTTP Client**: `reqwest`
- **Database**: `sqlx` (never `rusqlite` or `tokio-postgres`)

### Concurrency & Data Structures
- **Sync Primitives**: `tokio::sync` (never `std::sync` in async)
- **Concurrent Collections**: `dashmap` (never `Mutex<HashMap>`)
- **Channels**: `tokio::sync::{mpsc, broadcast, oneshot}`

### Configuration & Templates
- **Config Format**: YAML (never TOML for complex configs)
- **Config Loading**: `serde_yaml`
- **Templates**: `minijinja` (never `handlebars`)
- **Data Extraction**: `jsonpath-rust`

### Configuration Management
- **Multi-format Config**: `figment` with YAML/TOML/JSON support
- **Configuration Validation**: `validator` for compile-time guarantees
- **Hot Reloading**: `arc-swap` + `notify` for atomic updates
- **Environment Overrides**: Always use environment variable precedence

### Observability & Monitoring
- **Metrics Collection**: `prometheus` + lock-free atomic counters
- **Distributed Tracing**: `opentelemetry` + `tracing-opentelemetry`
- **Health Checks**: Custom health check frameworks with timeout handling
- **Concurrent Metrics**: `dashmap` for lock-free metrics storage
- **Logging**: `tracing` + `tracing-subscriber` (never `env_logger`)
- **Structured Logging**: Always use `#[instrument]` and context
- **Log Rotation**: `tracing-appender` for production

## ⚡ OPTIMIZATION FEATURES

- **Lazy Loading**: Feature-specific rules loaded only when needed
- **Context Preservation**: Project decisions cached across sessions
- **Template Generation**: Auto-generate boilerplate based on detected patterns
- **Incremental Updates**: Update only changed components
- **Workspace Dependencies**: Always prefer workspace deps over crate-specific

## 🚨 MANDATORY VERIFICATION

Before starting any Rust development:

```markdown
✓ RUST PROJECT VERIFICATION
- Project complexity determined? [SIMPLE/COMPLEX]
- Required features detected? [List]
- Appropriate rules loaded? [YES/NO]
- Cargo.toml structure verified? [YES/NO]
- Error handling strategy selected? [thiserror/anyhow]
- Uses Rust 2024 edition? [YES/NO]
- No unsafe code planned? [YES/NO]
- Workspace dependencies configured? [YES/NO]

→ If all verified: Proceed with development
→ If missing: Complete project setup
```

## 🔄 BUILD VERIFICATION WORKFLOW

Every code change must pass this sequence:

```bash
# 1. Build verification
cargo build

# 2. Test execution
cargo test

# 3. Linting verification
cargo clippy

# All three must pass before code is considered complete
```

This system ensures optimal Rust development practices while maintaining flexibility for different project scales and requirements, based on real-world production experience.
