---
description:
globs:
alwaysApply: false
---
# Dioxus 0.6 Best Practices

## Component Structure
- Use the `#[component]` macro for all components
- Keep components focused on a single responsibility
- Extract complex logic into custom hooks
- Name components using PascalCase
- Name hooks using the `use_` prefix and snake_case
- Return `Element` type from component functions
- Provide proper rustdoc comments for public components

## State Management
- Use `use_signal` for reactive state (preferred over deprecated `use_hook`)
- Use `read()` and `write()` methods for signal access
- Avoid direct mutations outside of write blocks
- Use `.set()` for simple signal updates
- Use `use_effect` for side effects and lifecycle events
- Keep state as close as possible to where it's used

## Hooks
- Create custom hooks for reusable logic
- Return `Signal<T>` from hooks that manage state
- Use proper tuple destructuring when returning multiple values
- Follow React-like naming conventions for hooks (`use_*`)
- Bundle related operations in a struct when returning multiple functions
- Use `use_effect` for initialization and cleanup
- Define clear input/output types for hooks

## Event Handling
- Create specific, named event handlers
- Use `move |param| { ... }` for event handlers
- Return handlers from hooks when they're related to that hook's state
- Box complex event handlers with proper type definitions
- Use event delegation when appropriate

## RSX Syntax
- Use proper indentation in `rsx!` blocks
- Group related attributes together
- Use string interpolation for dynamic class names: `class: "base-class {dynamic_class}"`
- Use shorthand for boolean properties
- Include proper ARIA attributes for accessibility

## Performance
- Use `use_memo` for expensive computations
- Define component props explicitly for complex components
- Avoid unnecessary re-renders
- Leverage Rust's borrowing system for references

## Storage
- Use abstracted storage operations (like the `utils::save` and `utils::load` functions)
- Handle storage errors gracefully
- Define constants for storage keys

## Styling
- Use consistent class naming conventions
- Leverage utility-first CSS frameworks like Tailwind
- Create helper functions for common style patterns
- Use theme-aware styling with dark/light mode support

## Persistence
- Save state to localStorage or other storage when needed
- Load state on component initialization
- Handle serialization/deserialization errors

## Testing
- Write unit tests for components with VirtualDom
- Test initial rendering, state changes, and event handling
- Use proper test assertions
- Structure tests with Arrange-Act-Assert pattern

## Platform Specifics
- Use feature flags for platform-specific code (`#[cfg(feature = "desktop")]`)
- Handle platform differences with conditional compilation
- Provide appropriate fallbacks for all platforms

## Project Structure
- Organize components in a logical directory structure
- Use modules to group related components
- Keep shared constants in a dedicated module
- Separate concerns: UI components, state management, and utilities

## Code Style
- Follow Rust naming conventions
- Use standard function signatures for event handlers
- Keep functions short and focused
- Use type aliases for complex types
- Use consistent parameter naming
