[package]
name = "simple-proxy"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2024"
license = "MIT"
documentation = "https://docs.rs/"
repository = "https://github.com/musakenshi/simple-proxy"
homepage = "https://github.com/musakenshi/simple-proxy"
description = """
A simple proxy server in Rust.
"""
readme = "README.md"
categories = ["development-tools"]
keywords = []

[dependencies]

anyhow = "1"
async-trait = "0.1.89"
http = "1.3.1"
jsonwebtoken = "9.3.1"
pingora = { version = "0.6.0", features = ["proxy", "lb", "rustls"] }
serde = { version = "1.0", features = ["derive"] }
serde_yaml = "0.9.34"
smcrypto = "0.3.1"
tokio = { version = "1.47.1", features = ["macros", "rt-multi-thread"] }
tower = "0.5.2"
tower-http = { version = "0.6", features = ["trace"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }


[dev-dependencies]
argon2 = "0.5.3"
axum = { version = "0.8", features = ["http2"] }
chrono = { version = "0.4", features = ["serde"] }
dashmap = "6.1"
rand = "0.8"
serde_json = "1.0"
tempfile = "3.0"

# # if you use pingora v0.4.0, you need to add sfv to your dependencies
# # running 'rm Cargo.lock' to clean Cargo.lock and rebuild your project
# [patch.crates-io]
# sfv = { git = "https://github.com/undef1nd/sfv.git", tag = "v0.9.4" }
# # *****************************************
