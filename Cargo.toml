[package]
name = "simple-proxy"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2024"
license = "MIT"
documentation = "https://docs.rs/"
repository = "https://github.com/musakenshi/simple-proxy"
homepage = "https://github.com/musakenshi/simple-proxy"
description = """
A simple proxy server in Rust.
"""
readme = "README.md"
categories = ["development-tools"]
keywords = []

[dependencies]

anyhow = "1.0.98"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.47.0", features = ["macros", "rt-multi-thread"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
tracing = "0.1.41"
tower = "0.5"
tower-http = { version = "0.6", features = ["trace"] }
pingora = { version = "0.5.0", features = ["proxy", "lb", "rustls"] }
async-trait = "0.1.88"
http = "1.3.1"
smcrypto = "0.3.1"
jsonwebtoken = "9.3.1"
serde_yaml = "0.9.34"


[dev-dependencies]
argon2 = "0.5.3"
axum = { version = "0.8", features = ["http2"] }
chrono = { version = "0.4", features = ["serde"] }
dashmap = "6.1"
rand = "0.8"
serde_json = "1.0"

# # if you use pingora v0.4.0, you need to add sfv to your dependencies
# # running 'rm Cargo.lock' to clean Cargo.lock and rebuild your project
# [patch.crates-io]
# sfv = { git = "https://github.com/undef1nd/sfv.git", tag = "v0.9.4" }
# # *****************************************
