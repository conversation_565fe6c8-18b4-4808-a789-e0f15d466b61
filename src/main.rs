
use pingora::prelude::*;
use tracing::info;
use simple_proxy::SimpleProxy;

fn main() -> Result<()> {
    
    tracing_subscriber::fmt::init();
    let mut server = Server::new(None)?;
    server.bootstrap();

    // let upstreams =
    //     LoadBalancer::try_from_iter(["1.1.1.1:443", "1.0.0.1:443"]).unwrap();
    
    let sp = SimpleProxy{};
    let proxy_addr = "0.0.0.0:8080";
    let mut proxy = http_proxy_service(&server.configuration, sp);
    proxy.add_tcp(proxy_addr);
    info!("Proxy server is running at {}", proxy_addr);
    server.add_service(proxy);

    server.run_forever();
}