use std::collections::HashMap;
use std::fs;
use std::convert::TryFrom;
use anyhow::{Result, anyhow, Context};
use crate::conf::raw::{SimpleProxyConfig, CertConfig, UpstreamConfig,GlobalConfig,ServerConfig};

#[derive(Debug, Clone)]
pub struct ProxyConfigResolved {
    pub global: GlobalConfigResolved,
    pub servers: HashMap<String, ServerConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct GlobalConfigResolved {
    pub port: u16,
    pub tls: Option<CertConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct CertConfigResolved {
    pub cert: String,
    pub key: String,
}

#[derive(Debug, Clone)]
pub struct ServerConfigResolved {
    pub tls: Option<CertConfigResolved>,
    pub upstream: String,
}

#[derive(Debug, <PERSON>lone)]
pub struct UpstreamConfigResolved {
    pub servers: Vec<String>,
}

impl TryFrom<&CertConfig> for CertConfigResolved {
    type Error = anyhow::Error;

    fn try_from(cert: &CertConfig) -> Result<Self, Self::Error> {
        let cert_path = cert.cert_path.as_path();
        let key_path = cert.key_path.as_path();

        // Load certificate and key contents
        let cert_content = fs::read_to_string(cert_path)
            .with_context(|| format!("Failed to load certificate from: {:?}", cert_path))?;

        let key_content = fs::read_to_string(key_path)
            .with_context(|| format!("Failed to load key from: {:?}", key_path))?;

        Ok(CertConfigResolved {
            cert: cert_content,
            key: key_content,
        })
    }
}

impl From<&UpstreamConfig> for UpstreamConfigResolved {
    fn from(upstream: &UpstreamConfig) -> Self {
        UpstreamConfigResolved {
            servers: upstream.servers.clone(),
        }
    }
}

impl TryFrom<SimpleProxyConfig> for ProxyConfigResolved {
    type Error = anyhow::Error;

    fn try_from(raw: SimpleProxyConfig) -> Result<Self, Self::Error> {
        // Build cert map for lookups
        let mut cert_map = HashMap::new();
        for cert in &raw.certs {
            let resolved_cert = CertConfigResolved::try_from(cert)?;
            cert_map.insert(cert.name.clone(), resolved_cert);
        }

        // Build upstream map for lookups
        let mut upstream_map = HashMap::new();
        for upstream in &raw.upstreams {
            let resolved_upstream = UpstreamConfigResolved::from(upstream);
            upstream_map.insert(upstream.name.clone(), resolved_upstream);
        }

        // Resolve global config
        let global = GlobalConfigResolved::try_from_with_map(&raw.global, & cert_map)?;

        // Resolve server configs
        let mut servers = HashMap::new();
        for server in raw.servers {
            let resolved_server = ServerConfigResolved::try_from_with_maps(&server, &cert_map)?;
            for server_name in server.server_name {
                if servers.contains_key(&server_name) {
                    return Err(anyhow!("Duplicate server name: {}", server_name));
                }
                servers.insert(server_name, resolved_server.clone());
            }
        }
        Ok(ProxyConfigResolved { global, servers })
    }
}

impl GlobalConfigResolved {
    fn try_from_with_map(
        global: &GlobalConfig,
        cert_map: &HashMap<String, CertConfigResolved>,
    ) -> Result<Self> {
        let tls = match & global.tls {
            Some(cert_name) => {
                let cert = cert_map
                    .get(cert_name)
                    .ok_or_else(|| anyhow!("Global TLS certificate: {} not found", cert_name))?;
                Some(cert.clone())
            }
            None => None,
        };
        Ok(GlobalConfigResolved { port: global.port, tls })
    }
}

impl ServerConfigResolved {
    fn try_from_with_maps(
        server: &ServerConfig,
        cert_map: &HashMap<String, CertConfigResolved>,
        upstream_map: &HashMap<String, UpstreamConfigResolved>,
    ) -> Result<Self> {
        // Resolve TLS for this server if configured
        let tls = match &server.tls {
            Some(cert_name) => {
                let cert = cert_map
                    .get(cert_name)
                    .ok_or_else(|| anyhow!("Server TLS certificate: {} not found", cert_name))?;
                Some(cert.clone())
            }
            None => None,
        };
        // Resolve upstream for this server
        let upstream_name = &server.upstream;
        let upstream = upstream_map
            .get(upstream_name)
            .ok_or_else(|| anyhow!("Upstream: {} not found", upstream_name))?.clone();
        Ok(ServerConfigResolved { tls, upstream })
    }
}