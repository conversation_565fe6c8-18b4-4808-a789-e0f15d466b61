use std::collections::HashMap;
use std::fs;
use std::convert::TryFrom;
use anyhow::{Result, anyhow, Context};
use crate::conf::raw::{SimpleProxyConfig, CertConfig, UpstreamConfig, GlobalConfig, ServerConfig};

#[derive(Debug, Clone)]
pub struct ProxyConfigResolved {
    pub global: GlobalConfigResolved,
    pub servers: HashMap<String, ServerConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct GlobalConfigResolved {
    pub port: u16,
    pub tls: Option<CertConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct CertConfigResolved {
    pub cert: String,
    pub key: String,
}

#[derive(Debug, Clone)]
pub struct ServerConfigResolved {
    pub tls: Option<CertConfigResolved>,
    pub upstream: UpstreamConfigResolved, // Store upstream name, not the full config
}

#[derive(Debug, <PERSON>lone)]
pub struct UpstreamConfigResolved {
    pub servers: Vec<String>,
}

// TryFrom implementation for CertConfig -> CertConfigResolved
impl TryFrom<&CertConfig> for CertConfigResolved {
    type Error = anyhow::Error;

    fn try_from(cert: &CertConfig) -> Result<Self, Self::Error> {
        let cert_content = fs::read_to_string(&cert.cert_path)
            .with_context(|| format!("Failed to read certificate file '{}'",
                cert.cert_path.display()))?;

        let key_content = fs::read_to_string(&cert.key_path)
            .with_context(|| format!("Failed to read key file '{}'",
                cert.key_path.display()))?;

        Ok(CertConfigResolved {
            cert: cert_content,
            key: key_content,
        })
    }
}

// From implementation for UpstreamConfig -> UpstreamConfigResolved
impl From<&UpstreamConfig> for UpstreamConfigResolved {
    fn from(upstream: &UpstreamConfig) -> Self {
        UpstreamConfigResolved {
            servers: upstream.servers.clone(),
        }
    }
}

// TryFrom implementation for SimpleProxyConfig -> ProxyConfigResolved
impl TryFrom<SimpleProxyConfig> for ProxyConfigResolved {
    type Error = anyhow::Error;

    fn try_from(raw: SimpleProxyConfig) -> Result<Self, Self::Error> {
        // Build certificate map for lookups
        let mut cert_map = HashMap::new();
        for cert in &raw.certs {
            let resolved_cert = CertConfigResolved::try_from(cert)?;
            cert_map.insert(cert.name.clone(), resolved_cert);
        }

        // Build upstream map for lookups
        let mut upstream_map = HashMap::new();
        for upstream in &raw.upstreams {
            let resolved_upstream = UpstreamConfigResolved::from(upstream);
            upstream_map.insert(upstream.name.clone(), resolved_upstream);
        }

        // Resolve global config
        let global = GlobalConfigResolved::try_from_with_cert_map(&raw.global, &cert_map)?;

        // Resolve server configs
        let mut servers = HashMap::new();
        for server in raw.servers {
            let resolved_server = ServerConfigResolved::try_from_with_maps(&server, &cert_map, &upstream_map)?;

            // Add an entry for each server name
            for server_name in server.server_name {
                if servers.contains_key(&server_name) {
                    return Err(anyhow!("Duplicate server name: '{}'", server_name));
                }
                servers.insert(server_name, resolved_server.clone());
            }
        }

        Ok(ProxyConfigResolved { global, servers })
    }
}

// Helper implementation for GlobalConfigResolved
impl GlobalConfigResolved {
    fn try_from_with_cert_map(
        global: &GlobalConfig,
        cert_map: &HashMap<String, CertConfigResolved>,
    ) -> Result<Self> {
        let tls = match &global.tls {
            Some(cert_name) => {
                let cert = cert_map
                    .get(cert_name)
                    .ok_or_else(|| anyhow!("Global TLS certificate '{}' not found", cert_name))?;
                Some(cert.clone())
            }
            None => None,
        };

        Ok(GlobalConfigResolved {
            port: global.port,
            tls,
        })
    }
}

// Helper implementation for ServerConfigResolved
impl ServerConfigResolved {
    fn try_from_with_maps(
        server: &ServerConfig,
        cert_map: &HashMap<String, CertConfigResolved>,
        upstream_map: &HashMap<String, UpstreamConfigResolved>,
    ) -> Result<Self> {
        // Verify upstream exists
        if !upstream_map.contains_key(&server.upstream) {
            return Err(anyhow!("Upstream '{}' not found", server.upstream));
        }

        // Resolve TLS certificate if specified
        let tls = match &server.tls {
            Some(cert_name) => {
                let cert = cert_map
                    .get(cert_name)
                    .ok_or_else(|| anyhow!("Server TLS certificate '{}' not found", cert_name))?;
                Some(cert.clone())
            }
            None => None,
        };

        // Get the upstream configuration
        let upstream_name = &server.upstream;
        let upstream = upstream_map
            .get(upstream_name)
            .ok_or_else(|| anyhow!("Upstream '{}' not found", upstream_name))?.clone();

        Ok(ServerConfigResolved {
            tls,
            upstream,
        })
    }
}

// Convenience methods for ProxyConfigResolved
impl ProxyConfigResolved {
    /// Get server configuration by server name
    pub fn get_server(&self, server_name: &str) -> Option<&ServerConfigResolved> {
        self.servers.get(server_name)
    }

    /// Get all server names
    pub fn get_server_names(&self) -> Vec<&String> {
        self.servers.keys().collect()
    }

    /// Check if a server name exists
    pub fn has_server(&self, server_name: &str) -> bool {
        self.servers.contains_key(server_name)
    }

    /// Get the number of configured servers
    pub fn server_count(&self) -> usize {
        self.servers.len()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use std::io::Write;

    fn create_temp_cert_files() -> Result<(NamedTempFile, NamedTempFile)> {
        let mut cert_file = NamedTempFile::new()?;
        let mut key_file = NamedTempFile::new()?;

        cert_file.write_all(b"-----BEGIN CERTIFICATE-----\ntest cert content\n-----END CERTIFICATE-----")?;
        key_file.write_all(b"-----BEGIN PRIVATE KEY-----\ntest key content\n-----END PRIVATE KEY-----")?;

        Ok((cert_file, key_file))
    }

    fn create_temp_cert_files_with_content(cert_content: &str, key_content: &str) -> Result<(NamedTempFile, NamedTempFile)> {
        let mut cert_file = NamedTempFile::new()?;
        let mut key_file = NamedTempFile::new()?;

        cert_file.write_all(cert_content.as_bytes())?;
        key_file.write_all(key_content.as_bytes())?;

        Ok((cert_file, key_file))
    }

    #[test]
    fn test_cert_config_try_from_success() -> Result<()> {
        let cert_content = "-----BEGIN CERTIFICATE-----\nMIIC...test cert...\n-----END CERTIFICATE-----";
        let key_content = "-----BEGIN PRIVATE KEY-----\nMIIE...test key...\n-----END PRIVATE KEY-----";
        let (cert_file, key_file) = create_temp_cert_files_with_content(cert_content, key_content)?;

        let cert_config = CertConfig {
            name: "test_cert".to_string(),
            cert_path: cert_file.path().to_path_buf(),
            key_path: key_file.path().to_path_buf(),
        };

        let resolved = CertConfigResolved::try_from(&cert_config)?;

        assert_eq!(resolved.cert, cert_content);
        assert_eq!(resolved.key, key_content);

        Ok(())
    }

    #[test]
    fn test_cert_config_try_from_missing_cert_file() {
        let cert_config = CertConfig {
            name: "test_cert".to_string(),
            cert_path: "/nonexistent/cert.pem".into(),
            key_path: "/nonexistent/key.pem".into(),
        };

        let result = CertConfigResolved::try_from(&cert_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Failed to read certificate file"));
    }

    #[test]
    fn test_cert_config_try_from_missing_key_file() -> Result<()> {
        let (cert_file, _) = create_temp_cert_files()?;

        let cert_config = CertConfig {
            name: "test_cert".to_string(),
            cert_path: cert_file.path().to_path_buf(),
            key_path: "/nonexistent/key.pem".into(),
        };

        let result = CertConfigResolved::try_from(&cert_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Failed to read key file"));

        Ok(())
    }

    #[test]
    fn test_upstream_config_from_single_server() {
        let upstream_config = UpstreamConfig {
            name: "api_servers".to_string(),
            servers: vec!["*************:8080".to_string()],
        };

        let resolved = UpstreamConfigResolved::from(&upstream_config);

        assert_eq!(resolved.servers.len(), 1);
        assert_eq!(resolved.servers[0], "*************:8080");
    }

    #[test]
    fn test_upstream_config_from_multiple_servers() {
        let upstream_config = UpstreamConfig {
            name: "web_servers".to_string(),
            servers: vec![
                "127.0.0.1:3000".to_string(),
                "127.0.0.1:3001".to_string(),
                "127.0.0.1:3002".to_string(),
            ],
        };

        let resolved = UpstreamConfigResolved::from(&upstream_config);

        assert_eq!(resolved.servers.len(), 3);
        assert_eq!(resolved.servers[0], "127.0.0.1:3000");
        assert_eq!(resolved.servers[1], "127.0.0.1:3001");
        assert_eq!(resolved.servers[2], "127.0.0.1:3002");
    }

    #[test]
    fn test_upstream_config_from_empty_servers() {
        let upstream_config = UpstreamConfig {
            name: "empty_servers".to_string(),
            servers: vec![],
        };

        let resolved = UpstreamConfigResolved::from(&upstream_config);

        assert_eq!(resolved.servers.len(), 0);
    }

    #[test]
    fn test_global_config_resolved_with_tls() -> Result<()> {
        let (cert_file, key_file) = create_temp_cert_files()?;

        let cert_config = CertConfig {
            name: "global_cert".to_string(),
            cert_path: cert_file.path().to_path_buf(),
            key_path: key_file.path().to_path_buf(),
        };

        let mut cert_map = HashMap::new();
        let resolved_cert = CertConfigResolved::try_from(&cert_config)?;
        cert_map.insert("global_cert".to_string(), resolved_cert);

        let global_config = GlobalConfig {
            port: 443,
            tls: Some("global_cert".to_string()),
        };

        let resolved = GlobalConfigResolved::try_from_with_cert_map(&global_config, &cert_map)?;

        assert_eq!(resolved.port, 443);
        assert!(resolved.tls.is_some());
        assert!(resolved.tls.as_ref().unwrap().cert.contains("test cert content"));

        Ok(())
    }

    #[test]
    fn test_global_config_resolved_without_tls() -> Result<()> {
        let cert_map = HashMap::new();

        let global_config = GlobalConfig {
            port: 8080,
            tls: None,
        };

        let resolved = GlobalConfigResolved::try_from_with_cert_map(&global_config, &cert_map)?;

        assert_eq!(resolved.port, 8080);
        assert!(resolved.tls.is_none());

        Ok(())
    }

    #[test]
    fn test_global_config_resolved_missing_cert() {
        let cert_map = HashMap::new();

        let global_config = GlobalConfig {
            port: 443,
            tls: Some("missing_cert".to_string()),
        };

        let result = GlobalConfigResolved::try_from_with_cert_map(&global_config, &cert_map);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Global TLS certificate 'missing_cert' not found"));
    }

    #[test]
    fn test_server_config_resolved_with_tls() -> Result<()> {
        let (cert_file, key_file) = create_temp_cert_files()?;

        // Setup cert map
        let cert_config = CertConfig {
            name: "server_cert".to_string(),
            cert_path: cert_file.path().to_path_buf(),
            key_path: key_file.path().to_path_buf(),
        };
        let mut cert_map = HashMap::new();
        let resolved_cert = CertConfigResolved::try_from(&cert_config)?;
        cert_map.insert("server_cert".to_string(), resolved_cert);

        // Setup upstream map
        let upstream_config = UpstreamConfig {
            name: "backend".to_string(),
            servers: vec!["127.0.0.1:3000".to_string()],
        };
        let mut upstream_map = HashMap::new();
        let resolved_upstream = UpstreamConfigResolved::from(&upstream_config);
        upstream_map.insert("backend".to_string(), resolved_upstream);

        let server_config = ServerConfig {
            server_name: vec!["example.com".to_string()],
            upstream: "backend".to_string(),
            tls: Some("server_cert".to_string()),
        };

        let resolved = ServerConfigResolved::try_from_with_maps(&server_config, &cert_map, &upstream_map)?;

        assert!(resolved.tls.is_some());
        assert_eq!(resolved.upstream.servers.len(), 1);
        assert_eq!(resolved.upstream.servers[0], "127.0.0.1:3000");

        Ok(())
    }

    #[test]
    fn test_server_config_resolved_without_tls() -> Result<()> {
        // Setup upstream map
        let upstream_config = UpstreamConfig {
            name: "backend".to_string(),
            servers: vec!["127.0.0.1:3000".to_string()],
        };
        let mut upstream_map = HashMap::new();
        let resolved_upstream = UpstreamConfigResolved::from(&upstream_config);
        upstream_map.insert("backend".to_string(), resolved_upstream);

        let cert_map = HashMap::new();

        let server_config = ServerConfig {
            server_name: vec!["example.com".to_string()],
            upstream: "backend".to_string(),
            tls: None,
        };

        let resolved = ServerConfigResolved::try_from_with_maps(&server_config, &cert_map, &upstream_map)?;

        assert!(resolved.tls.is_none());
        assert_eq!(resolved.upstream.servers.len(), 1);
        assert_eq!(resolved.upstream.servers[0], "127.0.0.1:3000");

        Ok(())
    }

    #[test]
    fn test_server_config_resolved_missing_upstream() -> Result<()> {
        let (cert_file, key_file) = create_temp_cert_files()?;

        // Setup cert map
        let cert_config = CertConfig {
            name: "server_cert".to_string(),
            cert_path: cert_file.path().to_path_buf(),
            key_path: key_file.path().to_path_buf(),
        };
        let mut cert_map = HashMap::new();
        let resolved_cert = CertConfigResolved::try_from(&cert_config)?;
        cert_map.insert("server_cert".to_string(), resolved_cert);

        let upstream_map = HashMap::new(); // Empty upstream map

        let server_config = ServerConfig {
            server_name: vec!["example.com".to_string()],
            upstream: "missing_upstream".to_string(),
            tls: Some("server_cert".to_string()),
        };

        let result = ServerConfigResolved::try_from_with_maps(&server_config, &cert_map, &upstream_map);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Upstream 'missing_upstream' not found"));

        Ok(())
    }

    #[test]
    fn test_server_config_resolved_missing_cert() -> Result<()> {
        // Setup upstream map
        let upstream_config = UpstreamConfig {
            name: "backend".to_string(),
            servers: vec!["127.0.0.1:3000".to_string()],
        };
        let mut upstream_map = HashMap::new();
        let resolved_upstream = UpstreamConfigResolved::from(&upstream_config);
        upstream_map.insert("backend".to_string(), resolved_upstream);

        let cert_map = HashMap::new(); // Empty cert map

        let server_config = ServerConfig {
            server_name: vec!["example.com".to_string()],
            upstream: "backend".to_string(),
            tls: Some("missing_cert".to_string()),
        };

        let result = ServerConfigResolved::try_from_with_maps(&server_config, &cert_map, &upstream_map);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Server TLS certificate 'missing_cert' not found"));

        Ok(())
    }

    #[test]
    fn test_simple_proxy_config_try_from_complete() -> Result<()> {
        let (cert_file1, key_file1) = create_temp_cert_files_with_content(
            "-----BEGIN CERTIFICATE-----\nweb cert content\n-----END CERTIFICATE-----",
            "-----BEGIN PRIVATE KEY-----\nweb key content\n-----END PRIVATE KEY-----"
        )?;
        let (cert_file2, key_file2) = create_temp_cert_files_with_content(
            "-----BEGIN CERTIFICATE-----\napi cert content\n-----END CERTIFICATE-----",
            "-----BEGIN PRIVATE KEY-----\napi key content\n-----END PRIVATE KEY-----"
        )?;

        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 443,
                tls: Some("web_cert".to_string()),
            },
            certs: vec![
                CertConfig {
                    name: "web_cert".to_string(),
                    cert_path: cert_file1.path().to_path_buf(),
                    key_path: key_file1.path().to_path_buf(),
                },
                CertConfig {
                    name: "api_cert".to_string(),
                    cert_path: cert_file2.path().to_path_buf(),
                    key_path: key_file2.path().to_path_buf(),
                }
            ],
            servers: vec![
                ServerConfig {
                    server_name: vec!["example.com".to_string(), "www.example.com".to_string()],
                    upstream: "web_servers".to_string(),
                    tls: Some("web_cert".to_string()),
                },
                ServerConfig {
                    server_name: vec!["api.example.com".to_string()],
                    upstream: "api_servers".to_string(),
                    tls: Some("api_cert".to_string()),
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "web_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string(), "127.0.0.1:3001".to_string()],
                },
                UpstreamConfig {
                    name: "api_servers".to_string(),
                    servers: vec!["127.0.0.1:4000".to_string()],
                }
            ],
        };

        let resolved = ProxyConfigResolved::try_from(raw_config)?;

        // Test global config
        assert_eq!(resolved.global.port, 443);
        assert!(resolved.global.tls.is_some());
        assert!(resolved.global.tls.as_ref().unwrap().cert.contains("web cert content"));

        // Test server configs
        assert_eq!(resolved.server_count(), 3);
        assert!(resolved.has_server("example.com"));
        assert!(resolved.has_server("www.example.com"));
        assert!(resolved.has_server("api.example.com"));

        let web_server = resolved.get_server("example.com").unwrap();
        assert!(web_server.tls.is_some());
        assert_eq!(web_server.upstream.servers.len(), 2);
        assert_eq!(web_server.upstream.servers[0], "127.0.0.1:3000");

        let api_server = resolved.get_server("api.example.com").unwrap();
        assert!(api_server.tls.is_some());
        assert_eq!(api_server.upstream.servers.len(), 1);
        assert_eq!(api_server.upstream.servers[0], "127.0.0.1:4000");

        // Test convenience methods
        let server_names = resolved.get_server_names();
        assert_eq!(server_names.len(), 3);
        assert!(server_names.contains(&&"example.com".to_string()));
        assert!(server_names.contains(&&"www.example.com".to_string()));
        assert!(server_names.contains(&&"api.example.com".to_string()));

        Ok(())
    }

    #[test]
    fn test_simple_proxy_config_try_from_minimal() -> Result<()> {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec!["localhost".to_string()],
                    upstream: "local_servers".to_string(),
                    tls: None,
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "local_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string()],
                }
            ],
        };

        let resolved = ProxyConfigResolved::try_from(raw_config)?;

        // Test global config
        assert_eq!(resolved.global.port, 8080);
        assert!(resolved.global.tls.is_none());

        // Test server configs
        assert_eq!(resolved.server_count(), 1);
        assert!(resolved.has_server("localhost"));

        let server = resolved.get_server("localhost").unwrap();
        assert!(server.tls.is_none());
        assert_eq!(server.upstream.servers.len(), 1);
        assert_eq!(server.upstream.servers[0], "127.0.0.1:3000");

        Ok(())
    }

    #[test]
    fn test_simple_proxy_config_try_from_missing_global_cert() {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 443,
                tls: Some("missing_cert".to_string()),
            },
            certs: vec![],
            servers: vec![],
            upstreams: vec![],
        };

        let result = ProxyConfigResolved::try_from(raw_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Global TLS certificate 'missing_cert' not found"));
    }

    #[test]
    fn test_simple_proxy_config_try_from_missing_server_cert() -> Result<()> {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec!["example.com".to_string()],
                    upstream: "web_servers".to_string(),
                    tls: Some("missing_cert".to_string()),
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "web_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string()],
                }
            ],
        };

        let result = ProxyConfigResolved::try_from(raw_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Server TLS certificate 'missing_cert' not found"));

        Ok(())
    }

    #[test]
    fn test_simple_proxy_config_try_from_missing_upstream() {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec!["example.com".to_string()],
                    upstream: "missing_upstream".to_string(),
                    tls: None,
                }
            ],
            upstreams: vec![],
        };

        let result = ProxyConfigResolved::try_from(raw_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Upstream 'missing_upstream' not found"));
    }

    #[test]
    fn test_simple_proxy_config_try_from_duplicate_server_names() -> Result<()> {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec!["example.com".to_string()],
                    upstream: "web_servers".to_string(),
                    tls: None,
                },
                ServerConfig {
                    server_name: vec!["example.com".to_string()], // Duplicate!
                    upstream: "web_servers".to_string(),
                    tls: None,
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "web_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string()],
                }
            ],
        };

        let result = ProxyConfigResolved::try_from(raw_config);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Duplicate server name: 'example.com'"));

        Ok(())
    }

    #[test]
    fn test_simple_proxy_config_try_from_multiple_server_names_same_config() -> Result<()> {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec![
                        "example.com".to_string(),
                        "www.example.com".to_string(),
                        "blog.example.com".to_string()
                    ],
                    upstream: "web_servers".to_string(),
                    tls: None,
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "web_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string()],
                }
            ],
        };

        let resolved = ProxyConfigResolved::try_from(raw_config)?;

        assert_eq!(resolved.server_count(), 3);
        assert!(resolved.has_server("example.com"));
        assert!(resolved.has_server("www.example.com"));
        assert!(resolved.has_server("blog.example.com"));

        // All should point to the same upstream configuration
        let server1 = resolved.get_server("example.com").unwrap();
        let server2 = resolved.get_server("www.example.com").unwrap();
        let server3 = resolved.get_server("blog.example.com").unwrap();

        assert_eq!(server1.upstream.servers, server2.upstream.servers);
        assert_eq!(server2.upstream.servers, server3.upstream.servers);
        assert_eq!(server1.upstream.servers[0], "127.0.0.1:3000");

        Ok(())
    }

    #[test]
    fn test_proxy_config_resolved_convenience_methods() -> Result<()> {
        let raw_config = SimpleProxyConfig {
            global: GlobalConfig {
                port: 8080,
                tls: None,
            },
            certs: vec![],
            servers: vec![
                ServerConfig {
                    server_name: vec!["example.com".to_string(), "www.example.com".to_string()],
                    upstream: "web_servers".to_string(),
                    tls: None,
                }
            ],
            upstreams: vec![
                UpstreamConfig {
                    name: "web_servers".to_string(),
                    servers: vec!["127.0.0.1:3000".to_string()],
                }
            ],
        };

        let resolved = ProxyConfigResolved::try_from(raw_config)?;

        // Test get_server
        assert!(resolved.get_server("example.com").is_some());
        assert!(resolved.get_server("www.example.com").is_some());
        assert!(resolved.get_server("nonexistent.com").is_none());

        // Test has_server
        assert!(resolved.has_server("example.com"));
        assert!(resolved.has_server("www.example.com"));
        assert!(!resolved.has_server("nonexistent.com"));

        // Test server_count
        assert_eq!(resolved.server_count(), 2);

        // Test get_server_names
        let server_names = resolved.get_server_names();
        assert_eq!(server_names.len(), 2);
        assert!(server_names.contains(&&"example.com".to_string()));
        assert!(server_names.contains(&&"www.example.com".to_string()));

        Ok(())
    }
}
