use std::collections::HashMap;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ProxyConfigResolved {
    pub global: GlobalConfigResolved,
    pub servers: HashMap<String, ServerConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct GlobalConfigResolved {
    pub port: u16,
    pub tls: Option<CertConfigResolved>,
}

#[derive(Debug, Clone)]
pub struct CertConfigResolved {
    pub cert: String,
    pub key: String,
}

#[derive(Debug, Clone)]
pub struct ServerConfigResolved {
    pub tls: Option<CertConfigResolved>,
    pub upstream: String,
}

#[derive(Debug, Clone)]
pub struct UpstreamConfigResolved {
    pub servers: Vec<String>,
}


