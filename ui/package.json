{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest run"}, "dependencies": {"@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.1.5", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.15.18", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/recharts": "^2.0.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.8", "typescript": "5.8.3", "typescript-eslint": "^8.26.1", "vite": "^6.3.4", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2"}}